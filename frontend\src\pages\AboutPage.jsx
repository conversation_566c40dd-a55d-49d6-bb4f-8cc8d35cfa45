import React from "react";
import ModelEvaluation from "../components/common/ModelEvaluation";

const AboutPage = () => {
  return (
    <div className="max-w-4xl mx-auto">
      {/* Model Evaluation Section */}
      <ModelEvaluation />

      {/* About Section */}
      <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
          Giới thiệu về ứng dụng X-Ray Analyzer
        </h2>
        <div className="prose max-w-3xl mx-auto">
          <p className="mb-4">
            <strong>X-<PERSON> Analyzer</strong> là một ứng dụng sử dụng trí tuệ nhân
            tạo để phát hiện các bất thường trong ảnh X-ray phổi. Ứng dụng sử
            dụng kỹ thuật học máy không giám sát (unsupervised learning) thông
            qua mô hình AutoEncoder để nhận diện các điểm bất thường.
          </p>

          <h3 className="text-xl font-semibold mt-6 mb-3">Cách hoạt động</h3>
          <p className="mb-3">
            AutoEncoder là một mô hình mạng nơ-ron được thiết kế để học cách mã
            hóa dữ liệu và sau đó tái tạo lại từ dữ liệu đã được mã hóa. Mô hình
            được huấn luyện
            <strong> chỉ trên ảnh X-ray phổi bình thường</strong> từ tập
            train/NORMAL/ và val/NORMAL/, giúp mô hình học được các đặc trưng
            của phổi khỏe mạnh.
          </p>
          <p className="mb-3">
            Khi một ảnh X-ray mới được đưa vào, mô hình sẽ cố gắng tái tạo lại
            ảnh. Nếu ảnh chứa các bất thường (như viêm phổi, nhiễm trùng...), mô
            hình sẽ gặp khó khăn trong việc tái tạo chính xác các vùng bất
            thường này vì nó chưa từng được huấn luyện trên dữ liệu bất thường.
            Bằng cách phân tích lỗi tái tạo (reconstruction error), chúng ta có
            thể phát hiện ra các vùng bất thường.
          </p>
          <p className="mb-3">
            <strong>Quá trình đánh giá:</strong> Sau khi huấn luyện, mô hình
            được đánh giá trên tập test bao gồm cả ảnh bình thường
            (test/NORMAL/) và ảnh viêm phổi (test/PNEUMONIA/). Ngưỡng phát hiện
            được xác định dựa trên phân phối lỗi tái tạo của tập validation bình
            thường.
          </p>

          <h3 className="text-xl font-semibold mt-6 mb-3">
            Ứng dụng trong y tế
          </h3>
          <p className="mb-3">
            Công nghệ này có thể hỗ trợ các bác sĩ trong việc sàng lọc ban đầu,
            giúp tiết kiệm thời gian và nâng cao hiệu quả trong chẩn đoán. Đặc
            biệt hữu ích trong các trường hợp:
          </p>
          <ul className="list-disc pl-5 mb-4">
            <li>Sàng lọc số lượng lớn ảnh X-ray</li>
            <li>Phát hiện sớm các dấu hiệu bất thường</li>
            <li>Hỗ trợ xác định vùng cần quan tâm cho bác sĩ</li>
            <li>Theo dõi sự tiến triển của bệnh qua thời gian</li>
          </ul>

          <div className="bg-blue-50 p-4 rounded-lg mt-6 mb-4">
            <p className="font-semibold text-blue-800">Lưu ý quan trọng</p>
            <p className="text-blue-700">
              Ứng dụng này chỉ là công cụ hỗ trợ và không thay thế chẩn đoán
              chính thức từ các chuyên gia y tế. Kết quả phân tích cần được xem
              xét và đánh giá bởi bác sĩ chuyên khoa.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutPage;
