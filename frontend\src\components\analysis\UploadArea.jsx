import React from "react";

const UploadArea = ({
  imageUrl,
  onDrop,
  onDragOver,
  handleImageChange,
  fileInputRef,
}) => {
  return (
    <div
      className={`w-full border-2 border-dashed rounded-lg p-4 flex flex-col items-center justify-center transition-colors duration-200
        ${
          imageUrl
            ? "border-blue-300 bg-blue-50"
            : "border-gray-300 bg-gray-50 hover:border-blue-200 hover:bg-gray-100"
        }`}
      onDrop={onDrop}
      onDragOver={onDragOver}
    >
      <input
        type="file"
        className="hidden"
        onChange={handleImageChange}
        accept="image/*"
        ref={fileInputRef}
      />
      {imageUrl ? (
        <div className="w-full">
          <div className="mb-4 text-center">
            <span className="text-blue-700 font-medium flex items-center justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-1"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              Ảnh đã chọn
            </span>
          </div>
          <div className="flex justify-center">
            <div className="relative overflow-hidden rounded-lg bg-black shadow-md">
              <img
                src={imageUrl}
                alt="X-ray preview"
                className="max-h-64 object-contain"
              />
            </div>
          </div>
          <div className="mt-4 text-center">
            <button
              onClick={() =>
                fileInputRef.current && fileInputRef.current.click()
              }
              className="text-blue-600 underline text-sm hover:text-blue-800 transition-colors"
            >
              Chọn ảnh khác
            </button>
          </div>
        </div>
      ) : (
        <div className="text-center p-6">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 mx-auto text-gray-400 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <p className="text-gray-700 font-medium mb-2">
            Kéo thả ảnh X-ray vào đây hoặc nhấp để chọn tập tin
          </p>
          <p className="text-gray-500 text-sm mb-4">
            Hỗ trợ định dạng JPG, PNG (kích thước tối đa 10MB)
          </p>
          <button
            onClick={() => fileInputRef.current && fileInputRef.current.click()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Chọn tập tin
          </button>
        </div>
      )}
    </div>
  );
};

export default UploadArea;
