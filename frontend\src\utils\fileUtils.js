/**
 * Convert a file object to a base64 string
 * @param {File} file - The file to convert
 * @returns {Promise<string>} Base64 encoded file
 */
export const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onloadend = () => {
      // Split to remove the data URL prefix (e.g., "data:image/jpeg;base64,")
      const base64data = reader.result.split(",")[1];
      resolve(base64data);
    };
    reader.onerror = (error) => reject(error);
  });
};

/**
 * Format file size in human-readable format
 * @param {number} bytes - Size in bytes
 * @returns {string} Formatted size string
 */
export const formatFileSize = (bytes) => {
  if (bytes < 1024) return bytes + " bytes";
  else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB";
  else if (bytes < 1073741824) return (bytes / 1048576).toFixed(1) + " MB";
  return (bytes / 1073741824).toFixed(1) + " GB";
};

/**
 * Validate image file
 * @param {File} file - The file to validate
 * @param {number} maxSizeBytes - Maximum allowed file size in bytes
 * @returns {Object} Validation result
 */
export const validateImageFile = (file, maxSizeBytes = 10 * 1024 * 1024) => {
  // Check if file is an image
  if (!file.type.startsWith("image/")) {
    return {
      valid: false,
      error: "Tập tin không phải là ảnh. Vui lòng chọn tập tin ảnh."
    };
  }

  // Check file size
  if (file.size > maxSizeBytes) {
    return {
      valid: false,
      error: `Kích thước tập tin quá lớn. Tối đa ${formatFileSize(maxSizeBytes)}.`
    };
  }
  return { valid: true };
};
