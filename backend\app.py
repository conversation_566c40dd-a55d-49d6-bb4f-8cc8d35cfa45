from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import load_model
from tensorflow.keras.preprocessing import image
import cv2
from io import BytesIO
import base64
from PIL import Image
import json

app = Flask(__name__)
CORS(app)

# Đường dẫn tới model đã được huấn luyện và ngưỡng phát hiện
MODEL_DIR = os.path.join(os.path.dirname(__file__), 'model', 'saved_model')
MODEL_PATH = os.path.join(MODEL_DIR, 'autoencoder.h5')
THRESHOLD_PATH = os.path.join(MODEL_DIR, 'threshold.txt')
EVALUATION_METRICS_PATH = os.path.join(MODEL_DIR, 'evaluation_metrics.json')

# Kích thước ảnh đầu vào cho model
IMG_SIZE = 128

# Ngưỡng phát hiện bất thường (sẽ được điều chỉnh sau khi huấn luyện)
ANOMALY_THRESHOLD = 0.015  # Gi<PERSON> trị mặc định nếu không đọc được từ file

# Tải model
model = None
try:
    # Load model với compile=False để tránh lỗi
    model = load_model(MODEL_PATH, compile=False)
    print("Model đã được tải thành công!")
except Exception as e:
    print(f"Lỗi khi tải model: {e}")
    print("Ứng dụng sẽ tải model khi có sẵn")

# Tải ngưỡng phát hiện bất thường
try:
    with open(THRESHOLD_PATH, 'r') as f:
        ANOMALY_THRESHOLD = float(f.read().strip())
    print(f"Ngưỡng phát hiện đã được tải thành công: {ANOMALY_THRESHOLD}")
except Exception as e:
    print(f"Lỗi khi tải ngưỡng từ {THRESHOLD_PATH}: {e}. Sử dụng giá trị mặc định: {ANOMALY_THRESHOLD}")

def preprocess_image(img_data):
    """Tiền xử lý ảnh từ base64 để đưa vào model"""
    try:
        # Giải mã ảnh base64
        img_bytes = base64.b64decode(img_data)
        img_pil = Image.open(BytesIO(img_bytes)).convert('L')  # Chuyển về ảnh xám

        # Thay đổi kích thước về kích thước đầu vào mong đợi
        img_pil = img_pil.resize((IMG_SIZE, IMG_SIZE))

        # Chuyển về numpy array và chuẩn hóa
        img_array = np.array(img_pil) / 255.0
        img_array = np.expand_dims(img_array, axis=-1)  # Thêm chiều kênh
        img_array = np.expand_dims(img_array, axis=0)   # Thêm chiều batch

        return img_array
    except Exception as e:
        print(f"Lỗi khi tiền xử lý ảnh: {e}")
        return None

def calculate_anomaly_score(original, reconstructed):
    """Tính điểm bất thường sử dụng BCE (phù hợp với loss function của model)"""
    # Sử dụng binary cross-entropy error (giống với training)
    bce = -np.mean(original * np.log(reconstructed + 1e-8) +
                   (1 - original) * np.log(1 - reconstructed + 1e-8))
    return bce

@app.route('/api/predict', methods=['POST'])
def predict():
    global model

    if not request.json or 'image' not in request.json:
        return jsonify({'error': 'Không có ảnh được cung cấp'}), 400

    img_data = request.json['image']

    # Loại bỏ prefix base64 nếu có
    if ',' in img_data:
        img_data = img_data.split(',')[1]

    # Đảm bảo model đã được tải
    if model is None:
        try:
            model = load_model(MODEL_PATH, compile=False)
        except Exception as e:
            return jsonify({'error': f'Model không khả dụng: {str(e)}'}), 500

    # Tiền xử lý ảnh
    img_array = preprocess_image(img_data)
    if img_array is None:
        return jsonify({'error': 'Lỗi khi xử lý ảnh'}), 400

    # Lấy ảnh tái tạo từ autoencoder
    reconstructed = model.predict(img_array)

    # Tính điểm bất thường
    anomaly_score = calculate_anomaly_score(img_array, reconstructed)

    # Xác định có phải bất thường hay không dựa trên ngưỡng
    is_anomaly = anomaly_score > ANOMALY_THRESHOLD

    # Tạo hình ảnh trực quan của ảnh tái tạo và ảnh khác biệt
    original_img = (img_array[0] * 255).astype(np.uint8)
    reconstructed_img = (reconstructed[0] * 255).astype(np.uint8)

    # Tính ảnh khác biệt
    diff_img = cv2.absdiff(original_img, reconstructed_img)
    diff_img = cv2.applyColorMap(diff_img, cv2.COLORMAP_JET)

    # Chuyển ảnh về base64 để hiển thị trên frontend
    reconstructed_base64 = base64_encode_image(reconstructed_img)
    diff_base64 = base64_encode_image(diff_img)

    return jsonify({
        'anomaly_score': float(anomaly_score),
        'is_anomaly': bool(is_anomaly),
        'classification': 'Viêm phổi' if is_anomaly else 'Bình thường',
        'reconstructed_image': reconstructed_base64,
        'difference_map': diff_base64,
        'threshold': ANOMALY_THRESHOLD
    })

def base64_encode_image(img_array):
    """Chuyển đổi mảng ảnh thành chuỗi base64"""
    if len(img_array.shape) == 3 and img_array.shape[2] == 1:
        img_array = img_array[:, :, 0]  # Loại bỏ chiều kênh nếu là ảnh xám

    img_pil = Image.fromarray(img_array)
    buffered = BytesIO()
    img_pil.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode('utf-8')
    return f'data:image/png;base64,{img_str}'

@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'model_loaded': model is not None})

@app.route('/api/model-evaluation', methods=['GET'])
def get_model_evaluation():
    """
    Trả về thông tin đánh giá mô hình từ file evaluation_metrics.json
    """
    try:
        if os.path.exists(EVALUATION_METRICS_PATH):
            with open(EVALUATION_METRICS_PATH, 'r') as f:
                metrics = json.load(f)
            return jsonify({
                'status': 'success',
                'metrics': metrics,
                'has_evaluation': True
            })
        else:
            return jsonify({
                'status': 'success',
                'message': 'Không tìm thấy file đánh giá mô hình. Vui lòng chạy đánh giá mô hình trước.',
                'has_evaluation': False
            })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Lỗi khi tải thông tin đánh giá mô hình: {str(e)}',
            'has_evaluation': False
        }), 500

if __name__ == '__main__':
    # Chạy ứng dụng Flask
    app.run(host='0.0.0.0', port=5000, debug=True)
