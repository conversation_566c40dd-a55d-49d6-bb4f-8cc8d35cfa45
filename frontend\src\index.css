@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: #3b82f6;
  --color-primary-dark: #2563eb;
  --color-secondary: #4f46e5;
  --color-secondary-dark: #4338ca;
  --color-text: #1f2937;
  --color-text-light: #4b5563;
  --color-background: #f9fafb;
  --color-background-alt: #f3f4f6;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--color-text);
  background-color: var(--color-background);
  min-height: 100vh;
  line-height: 1.6;
}

#root {
  padding: 0;
  width: 100%;
  min-height: 100vh;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Custom utility classes */
.text-balance {
  text-wrap: balance;
}

.prose {
  max-width: 65ch;
  line-height: 1.75;
}

.prose h1, .prose h2, .prose h3, .prose h4 {
  margin-top: 1.5em;
  margin-bottom: 0.75em;
  font-weight: 600;
  line-height: 1.25;
}

.prose p {
  margin-bottom: 1.25em;
}

.prose ul, .prose ol {
  margin-top: 0.5em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.prose li {
  margin-bottom: 0.5em;
}

/* Transition prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
  
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}