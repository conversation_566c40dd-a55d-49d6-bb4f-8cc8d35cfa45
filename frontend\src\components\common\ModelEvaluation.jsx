import React, { useState, useEffect } from 'react';
import { getModelEvaluation } from '../../services/apiService';

const ModelEvaluation = () => {
  const [evaluation, setEvaluation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchEvaluation();
  }, []);

  const fetchEvaluation = async () => {
    try {
      setLoading(true);
      const response = await getModelEvaluation();
      setEvaluation(response);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const formatPercentage = (value) => {
    return (value * 100).toFixed(1) + '%';
  };

  const formatNumber = (value) => {
    return typeof value === 'number' ? value.toFixed(4) : value;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
        <h3 className="text-xl font-semibold mb-4">Đ<PERSON>h giá hiệu suất mô hình</h3>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Đang tải dữ liệu đánh giá...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
        <h3 className="text-xl font-semibold mb-4">Đánh giá hiệu suất mô hình</h3>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700">Lỗi khi tải dữ liệu đánh giá: {error}</p>
          <button 
            onClick={fetchEvaluation}
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  if (!evaluation?.has_evaluation) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
        <h3 className="text-xl font-semibold mb-4">Đánh giá hiệu suất mô hình</h3>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-700">
            Chưa có dữ liệu đánh giá mô hình. Vui lòng chạy script đánh giá trước.
          </p>
          <p className="text-sm text-yellow-600 mt-2">
            Chạy lệnh: <code className="bg-yellow-100 px-2 py-1 rounded">python backend/model/evaluate.py</code>
          </p>
        </div>
      </div>
    );
  }

  const metrics = evaluation.metrics;

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
      <h3 className="text-xl font-semibold mb-6">Đánh giá hiệu suất mô hình</h3>
      
      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-semibold text-blue-800">Độ chính xác</h4>
          <p className="text-2xl font-bold text-blue-600">{formatPercentage(metrics.accuracy)}</p>
          <p className="text-sm text-blue-600">Accuracy</p>
        </div>
        
        <div className="bg-green-50 p-4 rounded-lg">
          <h4 className="font-semibold text-green-800">Độ chính xác dương</h4>
          <p className="text-2xl font-bold text-green-600">{formatPercentage(metrics.precision)}</p>
          <p className="text-sm text-green-600">Precision</p>
        </div>
        
        <div className="bg-purple-50 p-4 rounded-lg">
          <h4 className="font-semibold text-purple-800">Độ nhạy</h4>
          <p className="text-2xl font-bold text-purple-600">{formatPercentage(metrics.recall)}</p>
          <p className="text-sm text-purple-600">Recall</p>
        </div>
        
        <div className="bg-orange-50 p-4 rounded-lg">
          <h4 className="font-semibold text-orange-800">F1-Score</h4>
          <p className="text-2xl font-bold text-orange-600">{formatPercentage(metrics.f1_score)}</p>
          <p className="text-sm text-orange-600">F1-Score</p>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Thông tin chi tiết</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>ROC-AUC:</span>
              <span className="font-medium">{formatNumber(metrics.roc_auc)}</span>
            </div>
            <div className="flex justify-between">
              <span>Ngưỡng phát hiện:</span>
              <span className="font-medium">{formatNumber(metrics.threshold)}</span>
            </div>
            <div className="flex justify-between">
              <span>Tổng số mẫu:</span>
              <span className="font-medium">{metrics.total_samples}</span>
            </div>
            <div className="flex justify-between">
              <span>Mẫu bình thường:</span>
              <span className="font-medium">{metrics.normal_samples}</span>
            </div>
            <div className="flex justify-between">
              <span>Mẫu viêm phổi:</span>
              <span className="font-medium">{metrics.pneumonia_samples}</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Ma trận nhầm lẫn</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="bg-green-100 p-3 rounded text-center">
              <div className="font-bold text-green-800">{metrics.true_negatives}</div>
              <div className="text-green-600">True Negatives</div>
              <div className="text-xs text-green-500">Dự đoán đúng: Bình thường</div>
            </div>
            <div className="bg-red-100 p-3 rounded text-center">
              <div className="font-bold text-red-800">{metrics.false_positives}</div>
              <div className="text-red-600">False Positives</div>
              <div className="text-xs text-red-500">Dự đoán sai: Viêm phổi</div>
            </div>
            <div className="bg-red-100 p-3 rounded text-center">
              <div className="font-bold text-red-800">{metrics.false_negatives}</div>
              <div className="text-red-600">False Negatives</div>
              <div className="text-xs text-red-500">Dự đoán sai: Bình thường</div>
            </div>
            <div className="bg-green-100 p-3 rounded text-center">
              <div className="font-bold text-green-800">{metrics.true_positives}</div>
              <div className="text-green-600">True Positives</div>
              <div className="text-xs text-green-500">Dự đoán đúng: Viêm phổi</div>
            </div>
          </div>
        </div>
      </div>

      {/* Explanation */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-2">Giải thích các chỉ số</h4>
        <div className="text-sm text-blue-700 space-y-1">
          <p><strong>Accuracy:</strong> Tỷ lệ dự đoán đúng trên tổng số mẫu</p>
          <p><strong>Precision:</strong> Trong số các mẫu được dự đoán là viêm phổi, có bao nhiêu thực sự là viêm phổi</p>
          <p><strong>Recall:</strong> Trong số các mẫu thực sự là viêm phổi, có bao nhiêu được phát hiện đúng</p>
          <p><strong>F1-Score:</strong> Trung bình điều hòa của Precision và Recall</p>
          <p><strong>ROC-AUC:</strong> Diện tích dưới đường cong ROC, đo lường khả năng phân biệt của mô hình</p>
        </div>
      </div>

      {/* Refresh Button */}
      <div className="mt-4 text-center">
        <button 
          onClick={fetchEvaluation}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Làm mới dữ liệu
        </button>
      </div>
    </div>
  );
};

export default ModelEvaluation;
