// Services for API interactions
const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";

/**
 * Check the health of the API and model status
 * @returns {Promise<Object>} API health status
 */
export const checkApiHealth = async () => {
  try {
    const response = await fetch(`${API_URL}/api/health`);
    if (response.ok) {
      const data = await response.json();
      return {
        status: data.model_loaded ? "ready" : "no-model",
        data
      };
    } else {
      return { status: "error", error: `HTTP ${response.status}` };
    }
  } catch (error) {
    console.error("API health check failed:", error);
    return { status: "error", error: error.message };
  }
};

/**
 * Predict anomalies in the image
 * @param {String} base64Image - Base64 encoded image
 * @returns {Promise<Object>} Prediction result
 */
export const predictImage = async (base64Image) => {
  try {
    const response = await fetch(`${API_URL}/api/predict`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ image: base64Image }),
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error analyzing image:", error);
    throw error;
  }
};

/**
 * Get model evaluation metrics
 * @returns {Promise<Object>} Model evaluation metrics
 */
export const getModelEvaluation = async () => {
  try {
    const response = await fetch(`${API_URL}/api/model-evaluation`);
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching model evaluation:", error);
    throw error;
  }
};
