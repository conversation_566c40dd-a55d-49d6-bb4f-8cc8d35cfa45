# Hướng dẫn cài đặt và sử dụng X-Ray Analyzer

## 📖 Giới thiệu

X-Ray Analyzer là hệ thống phân tích ảnh X-quang ngực sử dụng mô hình AutoEncoder để phát hiện bất thường (viêm phổi). Hệ thống được huấn luyện chỉ với ảnh phổi khỏe mạnh và phát hiện bất thường thông qua lỗi tái tạo.

## 🖥️ Yêu cầu hệ thống

### Môi trường thực nghiệm:

- **Hệ điều hành**: Windows 10/11
- **Python**: 3.8 - 3.11
- **Node.js**: 16.x hoặc cao hơn

## 🔧 Cài đặt môi trường

### Bước 1: Cài đặt Python và Node.js

1. **Cài đặt Python**:

   - Tải Python từ: https://www.python.org/downloads/
   - <PERSON><PERSON><PERSON> "Add Python to PATH" khi cài đặt
   - <PERSON><PERSON><PERSON> tra: `python --version`

2. **Cài đặt Node.js**:
   - Tải Node.js từ: https://nodejs.org/
   - Kiểm tra: `node --version` và `npm --version`

### Bước 2: Cài đặt thư viện Python

```bash
# Di chuyển vào thư mục backend
cd backend

# Tạo môi trường ảo (khuyến nghị)
python -m venv venv
venv\Scripts\activate

# Cài đặt các thư viện cần thiết
pip install -r requirements.txt
```

**Danh sách thư viện chính**:

- `tensorflow==2.15.0` - Framework deep learning
- `flask==2.3.3` - Web framework cho backend
- `opencv-python==********` - Xử lý ảnh
- `numpy==1.24.3` - Tính toán số học
- `matplotlib==3.7.3` - Vẽ biểu đồ
- `scikit-learn==1.3.1` - Machine learning
- `pillow==10.0.1` - Xử lý ảnh
- `flask-cors==4.0.0` - CORS cho Flask

### Bước 3: Cài đặt thư viện Frontend

```bash
# Di chuyển vào thư mục frontend
cd frontend

# Cài đặt dependencies
npm install
```

**Thư viện chính**:

- `react` - Framework UI
- `react-router-dom` - Routing
- `vite` - Build tool
- `tailwindcss` - CSS framework

### Bước 4: Chuẩn bị dữ liệu

1. **Tải dataset**:

   - URL: https://www.kaggle.com/datasets/paultimothymooney/chest-xray-pneumonia
   - Giải nén vào thư mục `backend/dataset/`

2. **Cấu trúc thư mục**:

```
backend/dataset/
├── train/NORMAL/        # Ảnh huấn luyện bình thường
├── train/PNEUMONIA/     # Không sử dụng cho huấn luyện
├── val/NORMAL/          # Ảnh validation bình thường
├── val/PNEUMONIA/       # Không sử dụng cho huấn luyện
├── test/NORMAL/         # Ảnh test bình thường
└── test/PNEUMONIA/      # Ảnh test bệnh lý
```

### Bước 5: Huấn luyện và đánh giá mô hình

```bash
# Sử dụng script tự động (Khuyến nghị)
run_training.bat

# Hoặc chạy thủ công
cd backend/model
python train.py --data_dir "../../backend/dataset" --epochs 50
```

**Kết quả sau khi hoàn thành:**

- Model: `backend/model/saved_model/autoencoder.h5`
- Threshold: `backend/model/saved_model/threshold.txt`
- Metrics đánh giá: `backend/model/saved_model/evaluation_metrics.json`
- Biểu đồ training: `backend/model/training_history.png`
- Biểu đồ evaluation: `backend/model/saved_model/evaluation_results.png`

### Bước 6: Chạy ứng dụng

```bash
# Chạy cả backend và frontend
run_app.bat

# Hoặc chạy riêng lẻ:
# Backend (Terminal 1)
cd backend
python app.py

# Frontend (Terminal 2)
cd frontend
npm run dev
```

## 🔧 Cách sử dụng ứng dụng

### Truy cập ứng dụng:

- Mở trình duyệt và truy cập: http://localhost:5173

### Giao diện chính:

1. **Trang chủ**: Upload ảnh X-quang để phân tích

   - Chọn file ảnh (định dạng: .jpg, .jpeg, .png)
   - Click "Phân tích" để bắt đầu
   - Xem kết quả phân tích

2. **Trang About**: Xem thông tin chi tiết về mô hình và kết quả đánh giá

3. **Trang Guide**: Hướng dẫn sử dụng hệ thống

### Hiểu kết quả phân tích:

- **Normal**: Phổi khỏe mạnh (lỗi tái tạo thấp)
- **Pneumonia**: Có dấu hiệu viêm phổi (lỗi tái tạo cao)
- **Độ tin cậy**: Tỷ lệ phần trăm độ chắc chắn của kết quả

## 🔍 Khắc phục sự cố

### Lỗi "No images found":

- Kiểm tra cấu trúc thư mục dataset có đúng không
- Đảm bảo file ảnh có định dạng `.jpeg`

### Lỗi "Model not found":

- Chạy script `run_training.bat` để huấn luyện mô hình trước
- Kiểm tra thư mục `backend/model/saved_model/`

### Frontend không hiển thị kết quả:

- Đảm bảo backend đang chạy (port 5000)
- Kiểm tra console browser để xem lỗi chi tiết

### Lỗi cài đặt dependencies:

- Python: Sử dụng `pip install -r requirements.txt`
- Node.js: Sử dụng `npm install` trong thư mục frontend
