# Phát hiện bất thường trong ảnh X-ray sử dụng AutoEncoder

![<PERSON>-<PERSON> Analyzer](image/gui.png)

Ứng dụng web phát hiện bất thường trong ảnh X-ray phổi (ph<PERSON><PERSON> hiện viêm phổi) sử dụng mô hình AutoEncoder với ReactJS + Vite + TailwindCSS cho frontend và Flask + TensorFlow cho backend.

## Giới thiệu

Dự án X-Ray Analyzer phát triển bởi nhóm MOB, gồm các thành viên:

- Trần Công Minh
- <PERSON><PERSON> Đức Trung

Sinh viên trường Đại học Công Thương TP.HCM (HUIT), khoa Công nghệ thông tin.

## Tính năng chính

- Upload ảnh X-ray phổi từ máy tính (hỗ trợ kéo thả)
- <PERSON><PERSON>t hiện và phân loại ảnh X-ray (b<PERSON><PERSON> thường / viêm phổi)
- <PERSON><PERSON><PERSON> thị kết quả phân tích trực quan (<PERSON><PERSON>ố<PERSON>, <PERSON><PERSON> t<PERSON><PERSON> t<PERSON>, b<PERSON>n đồ nhiệt)
- Giao diện người dùng hiện đại, thân thiện, responsive
- Xử lý các trạng thái API và thông báo lỗi

## Công nghệ sử dụng

### Frontend

- **ReactJS**: Thư viện xây dựng giao diện người dùng
- **Vite**: Công cụ build nhanh, hiệu quả
- **TailwindCSS**: Framework CSS tiện dụng
- **React Router Dom**: Quản lý định tuyến
- **ES6+**: JavaScript hiện đại

### Backend

- **Flask**: Microframework Python
- **Flask-CORS**: Hỗ trợ Cross-Origin Resource Sharing
- **TensorFlow**: Framework Machine Learning
- **NumPy**: Xử lý mảng đa chiều
- **OpenCV**: Xử lý ảnh
- **Pillow**: Thư viện xử lý hình ảnh
- **Scikit-learn**: Công cụ machine learning

### Kiến trúc AI

- **AutoEncoder**: Mô hình học không giám sát
- Encoder: Mạng CNN với các lớp Convolutional, Batch Normalization, MaxPooling
- Decoder: Mạng CNN với các lớp Convolutional, Batch Normalization, Upsampling
- Loss function: Binary Crossentropy

## Yêu cầu hệ thống

- **Node.js** (v14+)
- **Python** (v3.8+)
- **pip** (Python package installer)

## Cấu trúc thư mục

```
AE_Xray/
├── backend/                    # Flask backend API
│   ├── app.py                  # API endpoints và xử lý ảnh
│   ├── requirements.txt        # Thư viện Python
│   ├── dataset/                # Dữ liệu train và test
│   │   ├── train/              # Ảnh huấn luyện (bình thường)
│   │   └── test/               # Ảnh kiểm tra (bất thường)
│   ├── model/                  # Định nghĩa và huấn luyện AutoEncoder
│   │   ├── autoencoder.py      # Cấu trúc mô hình
│   │   └── train.py            # Script huấn luyện mô hình
│   └── saved_model/            # Model đã huấn luyện
│
├── frontend/                   # React frontend
│   ├── src/
│   │   ├── components/         # UI Components
│   │   │   ├── analysis/       # Components phân tích ảnh
│   │   │   └── common/         # Components dùng chung
│   │   ├── layouts/            # Bố cục trang
│   │   ├── pages/              # Trang chính, giới thiệu, hướng dẫn
│   │   ├── services/           # API services
│   │   └── utils/              # Helper functions
│   ├── package.json            # Dependencies và scripts
│   ├── vite.config.js          # Cấu hình Vite
│   ├── tailwind.config.js      # Cấu hình Tailwind
│   └── index.html              # HTML template
│
└── image/                      # Ảnh minh họa
    └── gui.png                 # Ảnh giao diện ứng dụng
```

## Cài đặt và chạy dự án

### 1. Clone dự án

```bash
git clone https://github.com/dexter826/AE_Xray.git
cd AE_Xray
```

### 2. Cài đặt Backend

```bash
cd backend
pip install -r requirements.txt
```

### 3. Tải dữ liệu từ Kaggle

- Tải dataset từ https://www.kaggle.com/datasets/paultimothymooney/chest-xray-pneumonia
- Giải nén vào thư mục dataset
- Đảm bảo cấu trúc:
  ```
  backend/dataset/
  ├── train/
  │   ├── NORMAL/          # Ảnh phổi bình thường cho huấn luyện
  │   └── PNEUMONIA/       # Không sử dụng trong huấn luyện
  ├── val/
  │   ├── NORMAL/          # Ảnh phổi bình thường cho validation
  │   └── PNEUMONIA/       # Không sử dụng trong huấn luyện
  └── test/
      ├── NORMAL/          # Ảnh phổi bình thường cho đánh giá
      └── PNEUMONIA/       # Ảnh viêm phổi cho đánh giá
  ```

### 4. Huấn luyện mô hình

#### Cách 1: Sử dụng script tự động

```bash
run_training.bat
```

#### Cách 2: Chạy thủ công

```bash
cd backend/model
python train.py --data_dir "../../backend/dataset" --epochs 50
```

### 5. Đánh giá mô hình

#### Cách 1: Sử dụng script tự động

```bash
run_evaluation.bat
```

#### Cách 2: Chạy thủ công

```bash
cd backend/model
python evaluate.py --data_dir "../../backend/dataset"
```

### 6. Cài đặt Frontend

```bash
cd frontend
npm install
```

### 7. Chạy ứng dụng

**Chạy Backend:**

```bash
cd backend
python app.py
```

**Chạy Frontend:**

```bash
cd frontend
npm run dev
```

Truy cập ứng dụng tại: http://localhost:3000

## Quy trình hoạt động

1. **Upload ảnh X-ray:** Người dùng tải lên ảnh X-ray phổi
2. **Tiền xử lý ảnh:** Ảnh được xử lý về kích thước 128x128, chuyển sang thang độ xám
3. **Phân tích bằng AutoEncoder:** Model tái tạo ảnh và tính điểm bất thường
4. **Hiển thị kết quả:** Kết quả được hiển thị bao gồm:
   - Ảnh gốc
   - Ảnh được tái tạo bởi AutoEncoder
   - Bản đồ nhiệt thể hiện sự khác biệt giữa ảnh gốc và ảnh tái tạo
   - Điểm bất thường và kết luận (bình thường / viêm phổi)

## Nguyên lý hoạt động

AutoEncoder là một mạng neural học không giám sát, gồm 2 phần:

1. **Encoder:** Nén ảnh đầu vào thành một biểu diễn vector có số chiều thấp hơn
2. **Decoder:** Giải nén biểu diễn vector để tái tạo lại ảnh ban đầu

## Logic huấn luyện và đánh giá mới:

### Huấn luyện (Training):

- Mô hình AutoEncoder được huấn luyện **CHỈ** với ảnh bình thường từ:
  - `train/NORMAL/` - Tập dữ liệu chính để học các đặc trưng phổi khỏe mạnh
  - `val/NORMAL/` - Tập validation để kiểm tra hiệu suất và tránh overfitting

### Đánh giá (Evaluation):

- Sau khi huấn luyện, mô hình được đánh giá trên:
  - `test/NORMAL/` - Kỳ vọng lỗi tái tạo thấp (ảnh bình thường)
  - `test/PNEUMONIA/` - Kỳ vọng lỗi tái tạo cao (ảnh bất thường)

### Ngưỡng phát hiện:

- Được tính toán dựa trên phân phối lỗi tái tạo của tập validation NORMAL
- Lưu trong file `backend/model/saved_model/threshold.txt`

### Kết quả đánh giá:

- Các chỉ số: Accuracy, Precision, Recall, F1-Score, ROC-AUC
- Lưu trong file `backend/model/saved_model/evaluation_metrics.json`
- Hiển thị trực quan trong trang "Giới thiệu" của ứng dụng web

## Tác giả

**Nhóm MOB - HUIT:**

- Trần Công Minh
- Lê Đức Trung

Dự án được phát triển trong khuôn khổ môn học tại trường Đại học Công Thương TPHCM (HUIT), khoa Công nghệ thông tin.

## Giấy phép

© 2025 X-Ray Analyzer. Bản quyền được bảo lưu.

Phát triển bởi Nhóm MOB
