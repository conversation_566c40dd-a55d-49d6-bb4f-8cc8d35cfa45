import React from "react";
import { Link } from "react-router-dom";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-800 text-gray-200 w-full py-6">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <div className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18" />
                <line x1="7" y1="2" x2="7" y2="22" />
                <line x1="17" y1="2" x2="17" y2="22" />
                <line x1="2" y1="12" x2="22" y2="12" />
                <line x1="2" y1="7" x2="7" y2="7" />
                <line x1="2" y1="17" x2="7" y2="17" />
                <line x1="17" y1="17" x2="22" y2="17" />
                <line x1="17" y1="7" x2="22" y2="7" />
              </svg>
              <Link to="/" className="font-bold hover:text-white">
                X-Ray Analyzer
              </Link>
            </div>
            <p className="mt-2 text-sm text-gray-400">
              Phát hiện bất thường trong ảnh X-ray phổi sử dụng AutoEncoder
            </p>
          </div>

          <div className="text-sm text-gray-400 text-center md:text-right">
            <p>© {currentYear} X-Ray Analyzer. Bản quyền được bảo lưu.</p>
            <p className="mt-1">
              Phát triển bởi <span className="text-blue-400">Nhóm MOB</span>
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
