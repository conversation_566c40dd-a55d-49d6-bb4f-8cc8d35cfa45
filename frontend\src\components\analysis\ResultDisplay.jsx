import React, { useState } from "react";

function ResultDisplay({ result, originalImage }) {
  const [activeTab, setActiveTab] = useState("diagnosis");

  const {
    is_anomaly,
    anomaly_score,
    classification,
    reconstructed_image,
    difference_map,
    threshold,
  } = result;

  const scorePercentage = ((anomaly_score / threshold) * 100).toFixed(1);

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
      <div className="flex border-b">
        <button
          className={`flex-1 px-4 py-3 text-center font-medium transition-colors ${
            activeTab === "diagnosis"
              ? "bg-blue-50 text-blue-700 border-b-2 border-blue-600"
              : "text-gray-600 hover:bg-gray-50"
          }`}
          onClick={() => setActiveTab("diagnosis")}
        >
          Chẩn đoán
        </button>
        <button
          className={`flex-1 px-4 py-3 text-center font-medium transition-colors ${
            activeTab === "images"
              ? "bg-blue-50 text-blue-700 border-b-2 border-blue-600"
              : "text-gray-600 hover:bg-gray-50"
          }`}
          onClick={() => setActiveTab("images")}
        >
          Hình ảnh
        </button>
      </div>

      <div className="p-4">
        {activeTab === "diagnosis" && (
          <div>
            <div
              className={`p-4 mb-4 rounded-lg ${
                is_anomaly ? "bg-red-50" : "bg-green-50"
              }`}
            >
              <div className="flex items-center mb-2">
                {is_anomaly ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-red-600 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-green-600 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                )}
                <h3 className="text-lg font-medium">
                  {is_anomaly
                    ? "Phát hiện có bất thường"
                    : "Không phát hiện bất thường"}
                </h3>
              </div>
              <p className={is_anomaly ? "text-red-700" : "text-green-700"}>
                {is_anomaly
                  ? "Hệ thống đã phát hiện các khu vực có khả năng bất thường trong ảnh X-ray này."
                  : "Hệ thống không phát hiện các khu vực bất thường đáng kể trong ảnh X-ray này."}
              </p>
            </div>

            <div className="mb-4">
              <h4 className="font-medium text-gray-700 mb-1">
                Điểm bất thường:
              </h4>
              <div className="flex items-center">
                <div className="w-full bg-gray-200 rounded-full h-4 mr-3">
                  <div
                    className={`h-4 rounded-full ${
                      is_anomaly ? "bg-red-500" : "bg-green-500"
                    }`}
                    style={{
                      width: `${Math.min(scorePercentage, 100)}%`,
                    }}
                  ></div>
                </div>
                <span className="text-gray-700 font-medium">
                  {anomaly_score.toFixed(3)} / {threshold.toFixed(3)}
                </span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                {is_anomaly
                  ? `Ngưỡng phát hiện bất thường: ${threshold.toFixed(3)}`
                  : `Dưới ngưỡng bất thường ${threshold.toFixed(3)}`}
              </p>
            </div>

            <div className="p-3 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-700">Thông tin thêm:</h4>
              <p className="text-blue-700 text-sm mt-1">
                Kết quả này được tạo bằng cách sử dụng mô hình AutoEncoder học
                cách tái tạo ảnh X-ray bình thường. Mô hình sẽ khó tái tạo các
                vùng bất thường, giúp chúng tôi phát hiện ra chúng.
              </p>
            </div>
          </div>
        )}

        {activeTab === "images" && (
          <div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
              <div>
                <h4 className="text-center text-sm font-medium text-gray-600 mb-2">
                  Ảnh gốc
                </h4>
                <div className="bg-black rounded-lg overflow-hidden shadow-sm">
                  <img
                    src={originalImage}
                    alt="Original X-ray"
                    className="w-full h-auto object-contain"
                  />
                </div>
              </div>

              <div>
                <h4 className="text-center text-sm font-medium text-gray-600 mb-2">
                  Ảnh tái tạo
                </h4>                <div className="bg-black rounded-lg overflow-hidden shadow-sm">
                  <img
                    src={reconstructed_image}
                    alt="Reconstructed X-ray"
                    className="w-full h-auto object-contain"
                  />
                </div>
              </div>

              <div>
                <h4 className="text-center text-sm font-medium text-gray-600 mb-2">
                  Bản đồ khác biệt
                </h4>
                <div className="bg-black rounded-lg overflow-hidden shadow-sm">
                  <img
                    src={difference_map}
                    alt="Difference map"
                    className="w-full h-auto object-contain"
                  />
                </div>
              </div>
            </div>

            <div className="mt-4">
              <h4 className="font-medium text-gray-700 mb-2">Giải thích:</h4>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>
                  <strong>Ảnh gốc:</strong> Ảnh X-ray ban đầu được tải lên.
                </li>
                <li>
                  <strong>Ảnh tái tạo:</strong> Ảnh được mô hình tạo ra khi cố
                  gắng tái tạo ảnh gốc.
                </li>
                <li>
                  <strong>Bản đồ khác biệt:</strong> Hiển thị những khu vực khác
                  biệt giữa ảnh gốc và ảnh tái tạo. Các vùng sáng hơn cho thấy
                  nơi mô hình khó tái tạo - thường là các vùng bất thường.
                </li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default ResultDisplay;
