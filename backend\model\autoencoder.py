import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Input, Conv2D, MaxPooling2D, UpSampling2D, BatchNormalization, Dropout, Lambda
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau
import tensorflow.keras.backend as K

def build_autoencoder(input_shape=(128, 128, 1)):
    """
    Xây dựng Deep Convolutional AutoEncoder với Skip Connections cho hiệu suất cao

    Args:
        input_shape: <PERSON><PERSON><PERSON> thước <PERSON>nh đầu vào (mặc định 128x128 grayscale)

    Returns:
        model: Keras Model đã biên dịch với architecture mạnh mẽ
    """
    # Input
    input_img = Input(shape=input_shape)

    # ==================== ENCODER ====================
    # Block 1: 128x128 -> 64x64
    x1 = Conv2D(64, (3, 3), activation='relu', padding='same')(input_img)
    x1 = BatchNormalization()(x1)
    x1 = Conv2D(64, (3, 3), activation='relu', padding='same')(x1)
    x1 = BatchNormalization()(x1)
    skip1 = x1  # Skip connection
    x1 = MaxPooling2D((2, 2), padding='same')(x1)

    # Block 2: 64x64 -> 32x32
    x2 = Conv2D(128, (3, 3), activation='relu', padding='same')(x1)
    x2 = BatchNormalization()(x2)
    x2 = Conv2D(128, (3, 3), activation='relu', padding='same')(x2)
    x2 = BatchNormalization()(x2)
    skip2 = x2  # Skip connection
    x2 = MaxPooling2D((2, 2), padding='same')(x2)

    # Block 3: 32x32 -> 16x16
    x3 = Conv2D(256, (3, 3), activation='relu', padding='same')(x2)
    x3 = BatchNormalization()(x3)
    x3 = Conv2D(256, (3, 3), activation='relu', padding='same')(x3)
    x3 = BatchNormalization()(x3)
    skip3 = x3  # Skip connection
    x3 = MaxPooling2D((2, 2), padding='same')(x3)

    # Bottleneck: 16x16
    encoded = Conv2D(512, (3, 3), activation='relu', padding='same')(x3)
    encoded = BatchNormalization()(encoded)
    encoded = Conv2D(512, (3, 3), activation='relu', padding='same')(encoded)
    encoded = BatchNormalization()(encoded)

    # ==================== DECODER ====================
    # Block 1: 16x16 -> 32x32
    x = Conv2D(256, (3, 3), activation='relu', padding='same')(encoded)
    x = BatchNormalization()(x)
    x = UpSampling2D((2, 2))(x)
    x = tf.keras.layers.Concatenate()([x, skip3])  # Skip connection
    x = Conv2D(256, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)

    # Block 2: 32x32 -> 64x64
    x = Conv2D(128, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)
    x = UpSampling2D((2, 2))(x)
    x = tf.keras.layers.Concatenate()([x, skip2])  # Skip connection
    x = Conv2D(128, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)

    # Block 3: 64x64 -> 128x128
    x = Conv2D(64, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)
    x = UpSampling2D((2, 2))(x)
    x = tf.keras.layers.Concatenate()([x, skip1])  # Skip connection
    x = Conv2D(64, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)

    # Output layer
    decoded = Conv2D(1, (3, 3), activation='sigmoid', padding='same')(x)

    # ==================== MODEL COMPILATION ====================
    autoencoder = Model(input_img, decoded)

    # Sử dụng learning rate thấp hơn và loss function tốt hơn
    optimizer = tf.keras.optimizers.Adam(learning_rate=0.0001)

    autoencoder.compile(
        optimizer=optimizer,
        loss='binary_crossentropy',  # Thay đổi loss function
        metrics=['mse', 'mae']
    )

    return autoencoder

def get_callbacks(checkpoint_path):
    """
    Tạo callbacks nâng cao cho quá trình huấn luyện hiệu suất cao

    Args:
        checkpoint_path: Đường dẫn lưu model checkpoint

    Returns:
        list: Danh sách các callbacks nâng cao
    """
    checkpointer = ModelCheckpoint(
        filepath=checkpoint_path,
        monitor='val_loss',
        verbose=1,
        save_best_only=True,
        save_weights_only=False,
        mode='min'
    )

    # Tạm thời tắt early stopping để model học đủ
    early_stopping = EarlyStopping(
        monitor='loss',  # Monitor training loss thay vì val_loss
        patience=50,     # Patience rất cao
        restore_best_weights=False,  # Không restore về epoch cũ
        verbose=1,
        mode='min',
        min_delta=0.001  # Delta lớn hơn để tránh dừng sớm
    )

    # Learning rate reduction khi không cải thiện
    reduce_lr = ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.3,  # Giảm mạnh hơn
        patience=8,  # Đợi lâu hơn trước khi giảm
        min_lr=1e-8,
        verbose=1,
        mode='min',
        cooldown=5  # Đợi 5 epochs sau khi giảm LR
    )

    return [checkpointer, early_stopping, reduce_lr]