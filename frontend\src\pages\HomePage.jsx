import React, { useState, useRef, useEffect } from "react";
import UploadArea from "../components/analysis/UploadArea";
import ResultDisplay from "../components/analysis/ResultDisplay";
import LoadingSpinner from "../components/common/LoadingSpinner";
import ApiStatusAlert from "../components/common/ApiStatusAlert";
import { checkApiHealth, predictImage } from "../services/apiService";
import { fileToBase64, validateImageFile } from "../utils/fileUtils";

const HomePage = () => {
  const [image, setImage] = useState(null);
  const [imageUrl, setImageUrl] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [result, setResult] = useState(null);
  const [apiStatus, setApiStatus] = useState("unknown");

  const fileInputRef = useRef(null);

  // Check API health on component mount
  useEffect(() => {
    const fetchApiHealth = async () => {
      try {
        const healthResult = await checkApiHealth();
        setApiStatus(healthResult.status);
      } catch (error) {
        console.error("API health check failed:", error);
        setApiStatus("error");
      }
    };

    fetchApiHealth();
  }, []);

  const handleImageChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImage(file);
      setImageUrl(URL.createObjectURL(file));
      setResult(null);
      setError("");
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      setImage(file);
      setImageUrl(URL.createObjectURL(file));
      setResult(null);
      setError("");
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };
  const handleSubmit = async () => {
    if (!image) {
      setError("Vui lòng chọn ảnh X-ray trước khi phân tích.");
      return;
    }

    // Validate image file
    const validation = validateImageFile(image);
    if (!validation.valid) {
      setError(validation.error);
      return;
    }

    try {
      setLoading(true);
      setError("");

      // Convert image to base64 using utility function
      const base64data = await fileToBase64(image);
      
      // Use API service for prediction
      const data = await predictImage(base64data);
      setResult(data);
      setLoading(false);
    } catch (error) {
      console.error("Error analyzing image:", error);
      setError(`Lỗi: ${error.message}`);
      setLoading(false);
    }
  };

  const handleReset = () => {
    setImage(null);
    setImageUrl("");
    setResult(null);
    setError("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
      <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
        Phát hiện bất thường trong ảnh X-ray phổi sử dụng AutoEncoder
      </h2>

      <ApiStatusAlert status={apiStatus} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <UploadArea
            imageUrl={imageUrl}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            handleImageChange={handleImageChange}
            fileInputRef={fileInputRef}
          />

          {error && (
            <div className="mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          <div className="mt-6 flex flex-wrap justify-center sm:justify-start gap-4">
            <button
              onClick={handleSubmit}
              disabled={!image || loading || apiStatus !== "ready"}
              className={`px-6 py-2 rounded-lg font-medium text-white
                ${
                  !image || loading || apiStatus !== "ready"
                    ? "bg-blue-300 cursor-not-allowed"
                    : "bg-blue-600 hover:bg-blue-700"
                }`}
            >
              {loading ? "Đang phân tích..." : "Phân tích ảnh"}
            </button>

            <button
              onClick={handleReset}
              disabled={!image || loading}
              className={`px-6 py-2 rounded-lg font-medium 
                ${
                  !image || loading
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
            >
              Làm mới
            </button>
          </div>
        </div>

        <div>
          {loading ? (
            <LoadingSpinner />
          ) : result ? (
            <ResultDisplay result={result} originalImage={imageUrl} />
          ) : (
            <div className="h-full flex items-center justify-center border-2 border-dashed border-gray-200 rounded-lg p-8">
              <p className="text-gray-500 text-center">
                Kết quả phân tích sẽ hiển thị ở đây sau khi xử lý ảnh.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HomePage;
